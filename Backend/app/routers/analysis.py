"""
API endpoints for HFO analysis job management.
"""


from botocore.exceptions import ClientError
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, Field
from typing import List, Optional

from ..logging_config import get_logger
from ..services.analysis_service import AnalysisService

logger = get_logger(__name__)

router = APIRouter(prefix="/analysis", tags=["analysis"])


# Request/Response models
class AnalysisRequest(BaseModel):
    """Request model for submitting analysis job."""
    file_key: str
    parameters: dict | None = None


class BatchAnalysisFile(BaseModel):
    file_key: str = Field(..., description="S3 key for the EDF file")
    parameters: dict | None = None


class BatchAnalysisRequest(BaseModel):
    """Request model for batch analysis."""
    files: List[BatchAnalysisFile]


class AnalysisResponse(BaseModel):
    """Response model for analysis submission."""
    job_id: str
    status: str
    message: str


class BatchAnalysisResponse(BaseModel):
    """Response model for batch analysis."""
    batch_id: str
    job_ids: list[str]
    status: str
    message: str


class JobStatus(BaseModel):
    """Job status response model."""
    job_id: str
    status: str
    file_key: str | None = None
    created_at: str
    updated_at: str | None = None
    completed_at: str | None = None
    hfo_count: int | None = None
    results_url: str | None = None
    error_message: str | None = None


@router.post("/submit", response_model=AnalysisResponse)
async def submit_analysis(request: AnalysisRequest):
    """Submit a single EDF file for HFO analysis."""
    try:
        result = await AnalysisService.submit_single_job(
            file_key=request.file_key,
            parameters=request.parameters
        )

        return AnalysisResponse(**result)

    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e)) from e
    except Exception as e:
        logger.error(f"Error submitting analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.post("/batch", response_model=BatchAnalysisResponse)
async def submit_batch_analysis(request: BatchAnalysisRequest):
    """Submit multiple EDF files for batch HFO analysis."""
    try:
        file_keys = [file.file_key for file in request.files]
        per_file_params = {
            file.file_key: file.parameters for file in request.files}

        result = await AnalysisService.submit_batch_jobs(
            file_keys=file_keys,
            parameters=per_file_params
        )

        return BatchAnalysisResponse(**result)

    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e)) from e
    except Exception as e:
        logger.error(f"Error submitting batch analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.get("/jobs", response_model=list[JobStatus])
async def list_user_jobs(user_email: str | None = Query(default=None)):
    """List analysis jobs, optionally filtering by user email."""
    try:
        # Get real jobs from DynamoDB
        jobs = AnalysisService.get_user_jobs(user_email)

        if not jobs:
            logger.info(f"No jobs found for user {user_email}")
            return []

        return [
            JobStatus(
                job_id=job["job_id"],
                status=job["status"],
                file_key=job.get("file_key"),
                created_at=job["created_at"],
                updated_at=job.get("updated_at"),
                completed_at=job.get("completed_at"),
                hfo_count=job.get("hfo_count"),
                results_url=job.get("results_url"),
                error_message=job.get("error_message")
            )
            for job in jobs
        ]

    except Exception as e:
        logger.error(f"Error listing jobs: {e}")
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.get("/status/{job_id}", response_model=JobStatus)
async def get_job_status(job_id: str):
    """Get the status of an analysis job."""
    try:
        job = AnalysisService.get_job_by_id(job_id)

        if not job:
            raise HTTPException(status_code=404, detail="Job not found")

        return JobStatus(
            job_id=job["job_id"],
            status=job["status"],
            file_key=job.get("file_key"),
            created_at=job["created_at"],
            updated_at=job.get("updated_at"),
            completed_at=job.get("completed_at"),
            hfo_count=job.get("hfo_count"),
            results_url=job.get("results_url"),
            error_message=job.get("error_message"),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status: {e}")
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.get("/batch-status/{batch_id}")
async def get_batch_status(batch_id: str):
    """Get the status of all jobs in a batch."""
    try:
        jobs = AnalysisService.get_batch_jobs(batch_id)

        if not jobs:
            raise HTTPException(status_code=404, detail="Batch not found")

        summary = AnalysisService.summarize_batch_status(jobs)
        summary["batch_id"] = batch_id

        return summary

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting batch status: {e}")
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.get("/results/{job_id}")
async def get_analysis_results(job_id: str):
    """Get the analysis results for a completed job."""
    try:
        # Remove mock data - always use real results
        # mock_results = AnalysisService.get_mock_results(job_id)
        # if mock_results:
        #     return mock_results

        # Get job status
        job = AnalysisService.get_job_by_id(job_id)
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")

        if job["status"] != "completed":
            raise HTTPException(
                status_code=400,
                detail=f"Job is not completed. Current status: {job['status']}"
            )

        # Get results from S3
        try:
            results = AnalysisService.get_results_from_s3(job_id)
            return results
        except ClientError as ce:
            raise HTTPException(
                status_code=404, detail="Results not found") from ce

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting analysis results: {e}")
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.get("/download/{job_id}")
async def download_results(job_id: str, format: str = Query("json", regex="^(json|csv|report)$")):
    """Get a download link for analysis results.

    Formats:
    - json: Complete results with all data
    - csv: Simple HFO events list
    - report: Comprehensive analysis report (CSV format)
    """
    try:
        # Get job status
        job = AnalysisService.get_job_by_id(job_id)
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")

        if job["status"] != "completed":
            raise HTTPException(
                status_code=400,
                detail=f"Job is not completed. Current status: {job['status']}"
            )

        # Generate download URL
        download_url = AnalysisService.generate_download_url(job_id, format)

        return {"download_url": download_url, "format": format}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating download link: {e}")
        raise HTTPException(status_code=500, detail=str(e)) from e
