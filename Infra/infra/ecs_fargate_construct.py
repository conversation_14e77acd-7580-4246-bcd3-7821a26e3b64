# ECS Fargate infrastructure for HFO analysis processing
from aws_cdk import RemovalPolicy
from aws_cdk import aws_dynamodb as dynamodb
from aws_cdk import aws_ec2 as ec2
from aws_cdk import aws_ecr as ecr
from aws_cdk import aws_ecs as ecs
from aws_cdk import aws_iam as iam
from aws_cdk import aws_logs as logs
from aws_cdk import aws_s3 as s3
from aws_cdk import aws_sqs as sqs
from constructs import Construct

from .base_construct import BaseConstruct
from .ecs_scaling import ECSAutoScalingConfig
from .ses_construct import SESConstruct


class ECSFargateConstruct(BaseConstruct):
    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        vpc: ec2.IVpc,
        job_queue: sqs.IQueue,
        s3_bucket: s3.IBucket,
        jobs_table: dynamodb.ITable,
        preferences_table: dynamodb.ITable,
        ses_construct: SESConstruct,
        **kwargs,
    ) -> None:
        super().__init__(scope, construct_id)

        self.vpc = vpc
        self.job_queue = job_queue
        self.s3_bucket = s3_bucket
        self.jobs_table = jobs_table
        self.preferences_table = preferences_table
        self.ses_construct = ses_construct

        self._create_ecr_repository()
        self._create_ecs_infrastructure()
        self._create_task_definition()
        self._create_service()
        self._configure_auto_scaling()
        self._create_outputs()

    def _create_ecr_repository(self) -> None:
        """Create ECR repository for container images."""
        self.repository = ecr.Repository(
            self,
            "HFOProcessorRepository",
            repository_name="biormika-hfo-processor",
            removal_policy=RemovalPolicy.DESTROY,
            lifecycle_rules=[
                ecr.LifecycleRule(
                    max_image_count=10,
                    description="Remove old images",
                )
            ],
        )

    def _create_ecs_infrastructure(self) -> None:
        """Create ECS cluster and IAM roles."""
        # ECS Cluster
        self.cluster = ecs.Cluster(
            self,
            "HFOProcessingCluster",
            cluster_name="biormika-hfo-cluster",
            vpc=self.vpc,
            container_insights=True,
        )

        # Task execution role
        self.task_execution_role = self.create_service_role(
            "TaskExecutionRole",
            ["ecs-tasks.amazonaws.com"],
            "ECS task execution role for pulling images and writing logs",
            [
                iam.ManagedPolicy.from_aws_managed_policy_name(
                    "service-role/AmazonECSTaskExecutionRolePolicy"
                )
            ],
        )

        # Task role
        self.task_role = self.create_service_role(
            "TaskRole",
            ["ecs-tasks.amazonaws.com"],
            "ECS task role for application permissions",
        )

        # Grant permissions
        self.job_queue.grant_consume_messages(self.task_role)
        self.s3_bucket.grant_read_write(self.task_role)
        self.jobs_table.grant_read_write_data(self.task_role)
        self.preferences_table.grant_read_data(self.task_role)
        self.ses_construct.grant_send_email(self.task_role)

        # Log group
        self.log_group = self.create_log_group(
            "HFOProcessorLogs",
            "/ecs/biormika-hfo-processor",
            logs.RetentionDays.ONE_WEEK,
        )

    def _create_task_definition(self) -> None:
        """Create Fargate task definition and container."""
        # Task definition
        self.task_definition = ecs.FargateTaskDefinition(
            self,
            "HFOProcessorTaskDef",
            cpu=4096,
            memory_limit_mib=8192,
            execution_role=self.task_execution_role,
            task_role=self.task_role,
            family="biormika-hfo-processor",
            runtime_platform=ecs.RuntimePlatform(
                cpu_architecture=ecs.CpuArchitecture.ARM64,
                operating_system_family=ecs.OperatingSystemFamily.LINUX,
            ),
        )

        # Container
        environment = self.get_environment_variables(
            AWS_DEFAULT_REGION=self.node.scope.region,
            SQS_QUEUE_URL=self.job_queue.queue_url,
            S3_BUCKET_NAME=self.s3_bucket.bucket_name,
            JOBS_TABLE_NAME=self.jobs_table.table_name,
            PREFERENCES_TABLE_NAME=self.preferences_table.table_name,
            SES_SENDER_EMAIL=self.ses_construct.sender_email,
            LOG_LEVEL="INFO",
        )

        self.container = self.task_definition.add_container(
            "HFOProcessor",
            image=ecs.ContainerImage.from_ecr_repository(
                self.repository, "latest"),
            logging=ecs.LogDrivers.aws_logs(
                stream_prefix="hfo-processor",
                log_group=self.log_group,
            ),
            environment=environment,
            cpu=4096,
            memory_limit_mib=8192,
            essential=True,
        )

    def _create_service(self) -> None:
        """Create ECS Fargate service."""
        self.service = ecs.FargateService(
            self,
            "HFOProcessorService",
            cluster=self.cluster,
            task_definition=self.task_definition,
            service_name="biormika-hfo-processor",
            desired_count=1,  # Keep 1 warm container to eliminate cold starts
            assign_public_ip=True,
            min_healthy_percent=0,
            max_healthy_percent=200,
            capacity_provider_strategies=[
                ecs.CapacityProviderStrategy(
                    capacity_provider="FARGATE_SPOT",
                    weight=4,
                ),
                ecs.CapacityProviderStrategy(
                    capacity_provider="FARGATE",
                    weight=1,
                ),
            ],
        )

    def _configure_auto_scaling(self) -> None:
        """Configure auto-scaling for the service."""
        self.scaling_target = ECSAutoScalingConfig.configure_queue_depth_scaling(
            self.service,
            self.job_queue,
            min_capacity=1,  # Keep 1 warm container to eliminate cold starts
            max_capacity=10,
        )

    def _create_outputs(self) -> None:
        """Create CloudFormation outputs."""
        self.create_output(
            "ECRRepositoryURI",
            self.repository.repository_uri,
            "ECR repository URI for HFO processor",
        )

    def get_repository_uri(self) -> str:
        return self.repository.repository_uri
