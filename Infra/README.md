# Infrastructure - Cloud Foundation

## What This Does

The Infrastructure component defines and deploys all AWS cloud resources needed to run the Biormika platform. It uses AWS CDK (Cloud Development Kit) to create a scalable, secure, and cost-efficient architecture for processing brain recordings.

### Why Infrastructure as Code?

- **Reproducible**: Deploy identical environments (dev/staging/prod)
- **Version Controlled**: Track all infrastructure changes
- **Automated**: No manual AWS console configuration
- **Cost Optimized**: Resources scale to zero when unused

## Architecture Overview

### What Gets Created

```
User → CloudFront (CDN) → S3 (React App)
           ↓
      API Gateway → Lambda (Backend API)
           ↓
    ┌──────┴──────┬──────────┐
    S3          SQS       DynamoDB
 (Storage)    (Queue)    (Tracking)
                 ↓
            ECS Fargate
           (Processing)
```

### Component Purposes

| Resource | Purpose | Why Needed |
|----------|---------|------------|
| **S3 Buckets** | File storage | Store EDF files and results |
| **Lambda** | API server | Serverless backend (no idle costs) |
| **ECS Fargate** | HFO processor | Container-based processing |
| **SQS Queue** | Job management | Decouple upload from processing |
| **DynamoDB** | Status tracking | Real-time job status |
| **CloudFront** | CDN | Fast global content delivery |
| **SES** | Email service | Completion notifications |

## Quick Start

```bash
# Setup
cd Infra
python3 -m venv .venv && source .venv/bin/activate
pip install -r requirements.txt

# First-time bootstrap
cdk bootstrap --profile biormika

# Deploy everything
cdk deploy --profile biormika --outputs-file cdk-outputs.json

# Essential commands
cdk diff           # Preview changes
cdk deploy        # Apply changes
cdk destroy       # Remove all resources
cdk synth        # Generate CloudFormation
```

## Cost Optimization

### Pay-Per-Use Architecture

- **Lambda**: $0 when no API calls
- **ECS**: Scales to 0 tasks when queue empty
- **DynamoDB**: On-demand billing
- **S3**: Pay only for storage used

### Monthly Cost Estimate

| Service | Usage | Cost |
|---------|-------|------|
| Lambda | 10K requests | $2 |
| ECS | 100 hours | $15 |
| S3 | 100 GB | $3 |
| CloudFront | 10 GB transfer | $1 |
| **Total** | Light usage | **~$21/month** |

## Stack Components

### Storage (S3)

```python
# Three buckets created
1. Frontend bucket   # React app hosting
2. Files bucket     # EDF uploads
3. Results bucket   # Analysis outputs

# Security
- Server-side encryption (AES-256)
- Block all public access
- CORS for presigned URLs
- Lifecycle policies for old files
```

### Compute (Lambda + ECS)

```python
# Lambda Function
- Runtime: Python 3.9 ARM64
- Memory: 3 GB
- Timeout: 15 minutes
- Auto-scaling: Concurrent executions

# ECS Fargate
- CPU: 4 vCPU (ARM64)
- Memory: 8 GB
- Min tasks: 1 (warm container)
- Max tasks: 10
- Scale trigger: Queue depth
```

### Queue & Database

```python
# SQS FIFO Queue
- Message deduplication
- Order guarantee
- Visibility timeout: 30 min
- Dead letter queue

# DynamoDB Table
- On-demand capacity
- TTL: 30 days
- Partition key: job_id
- Global secondary index: user_email
```

## Project Structure

```
Infra/
├── infra/
│   ├── config.py                 # All configuration
│   ├── infra_stack.py           # Main stack
│   ├── s3_construct.py          # Storage resources
│   ├── lambda_construct.py      # API resources
│   ├── ecs_fargate_construct.py # Processing resources
│   ├── sqs_construct.py         # Queue resources
│   └── dynamodb_construct.py    # Database resources
├── app.py                       # CDK app entry
├── cdk.json                    # CDK configuration
└── requirements.txt            # Dependencies
```

## Configuration

### Stack Parameters (config.py)

```python
# Environment
ENVIRONMENT = "production"
AWS_REGION = "us-east-1"

# Resource Sizing
LAMBDA_MEMORY_MB = 3008
ECS_CPU_UNITS = 2048  # 2 vCPU
ECS_MEMORY_MB = 4096  # 4 GB

# Scaling
ECS_MIN_CAPACITY = 1  # Keep warm container
ECS_MAX_CAPACITY = 10
SCALE_OUT_QUEUE_DEPTH = 1

# Retention
DYNAMODB_TTL_DAYS = 30
CLOUDWATCH_LOG_RETENTION_DAYS = 7
```

## Deployment

### Initial Setup

```bash
# 1. Configure AWS credentials
aws configure --profile biormika

# 2. Bootstrap CDK (once per account/region)
cdk bootstrap aws://ACCOUNT/us-east-1 --profile biormika

# 3. Deploy stack
cdk deploy --profile biormika
```

### Update Infrastructure

```bash
# Preview changes
cdk diff --profile biormika

# Apply changes
cdk deploy --profile biormika --require-approval never

# Output values to file
cdk deploy --outputs-file outputs.json --profile biormika
```

### Destroy Resources

```bash
# Remove everything (WARNING: deletes data)
cdk destroy --profile biormika
```

## Security

### IAM Roles

Each service has least-privilege access:

```
Lambda Role:
- S3: Read/Write to specific buckets
- SQS: Send messages
- DynamoDB: Read/Write
- CloudWatch: Write logs

ECS Task Role:
- S3: Read EDF files, write results
- SQS: Receive/Delete messages
- DynamoDB: Update job status
- SES: Send emails
```

### Network Security

- **VPC**: Private subnets for ECS
- **Security Groups**: Minimal ingress rules
- **S3**: VPC endpoints for private access
- **API Gateway**: Throttling enabled

### Data Security

- **Encryption**: At rest and in transit
- **Backups**: S3 versioning enabled
- **Access Logs**: CloudTrail for audit
- **CORS**: Restricted origins

## Monitoring

```bash
# Stack status
aws cloudformation describe-stacks \
  --stack-name BiormikaStack --profile biormika

# Resource usage
aws cloudwatch get-metric-statistics \
  --namespace AWS/Lambda \
  --metric-name Duration \
  --dimensions Name=FunctionName,Value=BiormikaStack-ApiFunction \
  --start-time 2024-01-01T00:00:00Z \
  --end-time 2024-01-02T00:00:00Z \
  --period 3600 \
  --statistics Average \
  --profile biormika

# Cost explorer
aws ce get-cost-and-usage \
  --time-period Start=2024-01-01,End=2024-01-31 \
  --granularity MONTHLY \
  --metrics BlendedCost \
  --group-by Type=SERVICE \
  --profile biormika
```

## Troubleshooting

| Issue | Solution |
|-------|----------|
| Stack rollback | Check CloudFormation events for error |
| Permission denied | Verify IAM roles and policies |
| Resource limit | Request quota increase |
| High costs | Check CloudWatch metrics, enable cost alerts |

## Output Values

After deployment, CDK outputs important values:

```json
{
  "ApiEndpoint": "https://xxx.execute-api.region.amazonaws.com",
  "FrontendBucket": "biormika-frontend-xxx",
  "FilesBucket": "biormika-files-xxx",
  "CloudFrontUrl": "https://dxxx.cloudfront.net",
  "QueueUrl": "https://sqs.region.amazonaws.com/xxx",
  "TableName": "BiormikaHFOJobTable"
}
```

Use these in Backend/.env and deployment scripts.

## Best Practices

1. **Tag Everything**: Use consistent tags for cost tracking
2. **Use Outputs**: Export values for cross-stack references
3. **Version Lock**: Pin CDK version in requirements.txt
4. **Test Changes**: Use `cdk diff` before deploying
5. **Monitor Costs**: Set up billing alerts

## Dependencies

- **aws-cdk-lib**: CDK v2 framework
- **constructs**: CDK construct library
- **python-dotenv**: Environment management

## License

Copyright 2024 Biormika. All rights reserved.