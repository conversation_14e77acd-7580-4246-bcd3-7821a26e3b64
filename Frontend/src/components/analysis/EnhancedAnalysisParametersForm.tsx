import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Settings, ChevronDown, ChevronRight } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";

export interface AnalysisParameters {
  frequency: {
    low_cutoff: number;
    high_cutoff: number;
  };
  thresholds: {
    amplitude_1: number; // StdDev above energy signal
    amplitude_2: number; // StdDev above baseline
    peaks_1: number; // Min peaks in HFO
    peaks_2: number; // Min peaks above threshold
    duration: number; // Min HFO length (ms)
    temporal_sync: number; // Inter-HFO interval in any channel
    spatial_sync: number; // Inter-HFO interval across channels
  };
  montage: {
    type: "bipolar" | "average" | "referential";
    reference?: string;
  };
  channelSelection?: {
    selectedChannels: string[];
  };
  timeSegment?: {
    mode: "entire_file" | "start_end_times" | "start_duration";
    startTime?: number;
    endTime?: number;
    duration?: number;
  };
  analysis_start?: number;
  analysis_end?: number;
}

interface EnhancedAnalysisParametersFormProps {
  onParametersChange: (params: AnalysisParameters) => void;
  onSubmit: (params: AnalysisParameters) => void;
  isSubmitting?: boolean;
  channels?: string[];
  fileInfo?: {
    filename: string;
    samplingRate: number;
    duration: number;
    startDate?: string;
    startTime?: string;
  };
}

const selectClass = "w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white";
const inputClass = "w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent";
const labelClass = "flex items-center gap-2 text-sm font-medium text-gray-700";
const descriptionClass = "text-xs text-gray-500 mt-1";

export const EnhancedAnalysisParametersForm: React.FC<EnhancedAnalysisParametersFormProps> = ({
  onParametersChange,
  onSubmit,
  isSubmitting = false,
  channels = [],
  fileInfo,
}) => {
  // State for tracking expanded channel groups
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({});

  // Default parameters (aligned with sample project)
  const [parameters, setParameters] = useState<AnalysisParameters>({
    frequency: {
      low_cutoff: 50,
      high_cutoff: 300,
    },
    thresholds: {
      amplitude_1: 2,
      amplitude_2: 2,
      peaks_1: 6,
      peaks_2: 3,
      duration: 10,
      temporal_sync: 10,
      spatial_sync: 10,
    },
    montage: {
      type: "bipolar",
    },
    channelSelection: {
      selectedChannels: channels, // Select all by default
    },
    timeSegment: {
      mode: "entire_file",
    },
  });

  const updateParameter = (section: string, field: string, value: string | number | boolean | string[] | undefined) => {
    if (section === "root") {
      const updated = {
        ...parameters,
        [field]: value === "" ? undefined : value,
      };
      setParameters(updated);
      onParametersChange(updated);
    } else {
      const sectionData = parameters[section as keyof AnalysisParameters];
      if (typeof sectionData === "object" && sectionData !== null) {
        const updated = {
          ...parameters,
          [section]: {
            ...sectionData,
            [field]: value,
          },
        };
        setParameters(updated);
        onParametersChange(updated);
      }
    }
  };

  const handleChannelSelection = (channel: string) => {
    const currentSelection = parameters.channelSelection?.selectedChannels || [];
    const updated = currentSelection.includes(channel) ? currentSelection.filter((ch) => ch !== channel) : [...currentSelection, channel];

    updateParameter("channelSelection", "selectedChannels", updated);
  };

  const selectAllChannels = () => {
    updateParameter("channelSelection", "selectedChannels", channels);
  };

  const deselectAllChannels = () => {
    updateParameter("channelSelection", "selectedChannels", []);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Map time segment to analysis_start/end for backward compatibility
    if (parameters.timeSegment) {
      if (parameters.timeSegment.mode === "start_end_times") {
        parameters.analysis_start = parameters.timeSegment.startTime;
        parameters.analysis_end = parameters.timeSegment.endTime;
      } else if (parameters.timeSegment.mode === "start_duration") {
        parameters.analysis_start = parameters.timeSegment.startTime;
        parameters.analysis_end = (parameters.timeSegment.startTime || 0) + (parameters.timeSegment.duration || 0);
      }
    }

    onSubmit(parameters);
  };

  const selectedChannelCount = parameters.channelSelection?.selectedChannels.length || 0;

  // Group channels by prefix (matching original desktop implementation)
  const groupChannelsByPrefix = (channels: string[]) => {
    const groups: Record<string, string[]> = {};
    const excludedTypes = ["EKG", "REF", "E", "C"]; // Types to exclude from grouping

    channels.forEach((channel) => {
      let prefix = "Other";

      // First try original format: "P RG1" or "POL RT2"
      const originalMatch = channel.match(/^(P|POL)\s([A-Za-z]+)/);
      if (originalMatch) {
        prefix = originalMatch[2]; // Extract "RG", "RT", etc.
      } else {
        // Fallback to simple prefix extraction: "PRG1" -> "PRG"
        const simpleMatch = channel.match(/^([A-Za-z]+)/);
        if (simpleMatch) {
          prefix = simpleMatch[1];
        }
      }

      // Skip excluded channel types
      if (excludedTypes.includes(prefix.toUpperCase())) {
        return;
      }

      if (!groups[prefix]) {
        groups[prefix] = [];
      }
      groups[prefix].push(channel);
    });

    // Sort groups by name and sort channels within each group
    const sortedGroups: Record<string, string[]> = {};
    Object.keys(groups)
      .sort()
      .forEach((key) => {
        sortedGroups[key] = groups[key].sort();
      });

    return sortedGroups;
  };

  const channelGroups = groupChannelsByPrefix(channels);

  // Auto-expand all groups when channels are first loaded
  useEffect(() => {
    if (channels.length > 0) {
      const groups = groupChannelsByPrefix(channels);
      const allGroups = Object.keys(groups);
      const expandedState: Record<string, boolean> = {};
      allGroups.forEach((group) => {
        expandedState[group] = true;
      });
      setExpandedGroups(expandedState);
    }
  }, [channels]); // Re-run when channels change

  const handleGroupSelection = (groupPrefix: string, select: boolean) => {
    const currentSelection = parameters.channelSelection?.selectedChannels || [];
    const groupChannels = channelGroups[groupPrefix];

    let updated: string[];
    if (select) {
      // Add all group channels that aren't already selected
      updated = [...new Set([...currentSelection, ...groupChannels])];
    } else {
      // Remove all group channels
      updated = currentSelection.filter((ch) => !groupChannels.includes(ch));
    }

    updateParameter("channelSelection", "selectedChannels", updated);
  };

  const isGroupFullySelected = (groupPrefix: string) => {
    const currentSelection = parameters.channelSelection?.selectedChannels || [];
    const groupChannels = channelGroups[groupPrefix];
    return groupChannels.every((ch) => currentSelection.includes(ch));
  };

  const isGroupPartiallySelected = (groupPrefix: string) => {
    const currentSelection = parameters.channelSelection?.selectedChannels || [];
    const groupChannels = channelGroups[groupPrefix];
    const selectedInGroup = groupChannels.filter((ch) => currentSelection.includes(ch));
    return selectedInGroup.length > 0 && selectedInGroup.length < groupChannels.length;
  };

  const toggleGroupExpansion = (groupPrefix: string) => {
    setExpandedGroups((prev) => ({
      ...prev,
      [groupPrefix]: !prev[groupPrefix],
    }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* File Information Display */}
      {fileInfo && (
        <Card className="bg-gray-50">
          <CardHeader>
            <CardTitle className="text-lg">File Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Filename:</span> {fileInfo.filename}
              </div>
              <div>
                <span className="font-medium">Sampling Rate:</span> {fileInfo.samplingRate} Hz
              </div>
              <div>
                <span className="font-medium">Duration:</span> {fileInfo.duration} seconds
              </div>
              <div>
                <span className="font-medium">Channels:</span> {channels.length} available
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Threshold Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Threshold Settings
          </CardTitle>
          <CardDescription>Configure HFO detection sensitivity parameters</CardDescription>
        </CardHeader>
        <CardContent className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <label htmlFor="amplitude-1" className={labelClass}>
              Amplitude 1 *
            </label>
            <select
              id="amplitude-1"
              value={parameters.thresholds.amplitude_1}
              onChange={(e) => updateParameter("thresholds", "amplitude_1", Number(e.target.value))}
              className={selectClass}
            >
              <option value={2}>2</option>
              <option value={3}>3</option>
              <option value={4}>4</option>
              <option value={5}>5</option>
            </select>
            <p className={descriptionClass}>HFO amp ≥ energy signal (times std)</p>
          </div>

          <div className="space-y-2">
            <label htmlFor="amplitude-2" className={labelClass}>
              Amplitude 2 *
            </label>
            <select
              id="amplitude-2"
              value={parameters.thresholds.amplitude_2}
              onChange={(e) => updateParameter("thresholds", "amplitude_2", Number(e.target.value))}
              className={selectClass}
            >
              <option value={2}>2</option>
              <option value={3}>3</option>
              <option value={4}>4</option>
              <option value={5}>5</option>
            </select>
            <p className={descriptionClass}>HFO amp ≥ mean baseline signal (% times std)</p>
          </div>

          <div className="space-y-2">
            <label htmlFor="peaks-1" className={labelClass}>
              Peaks 1 *
            </label>
            <select
              id="peaks-1"
              value={parameters.thresholds.peaks_1}
              onChange={(e) => updateParameter("thresholds", "peaks_1", Number(e.target.value))}
              className={selectClass}
            >
              <option value={6}>6</option>
              <option value={7}>7</option>
              <option value={8}>8</option>
            </select>
            <p className={descriptionClass}>HFO ≥ Amplitude 1 (# peaks)</p>
          </div>

          <div className="space-y-2">
            <label htmlFor="peaks-2" className={labelClass}>
              Peaks 2 *
            </label>
            <select
              id="peaks-2"
              value={parameters.thresholds.peaks_2}
              onChange={(e) => updateParameter("thresholds", "peaks_2", Number(e.target.value))}
              className={selectClass}
            >
              <option value={3}>3</option>
              <option value={4}>4</option>
              <option value={5}>5</option>
              <option value={6}>6</option>
            </select>
            <p className={descriptionClass}>HFO ≥ Amplitude 2 (# peaks)</p>
          </div>

          <div className="space-y-2">
            <label htmlFor="duration" className={labelClass}>
              Duration (ms)
            </label>
            <select
              id="duration"
              value={parameters.thresholds.duration}
              onChange={(e) => updateParameter("thresholds", "duration", Number(e.target.value))}
              className={selectClass}
            >
              <option value={10}>10 ms</option>
              <option value={12}>12 ms</option>
              <option value={15}>15 ms</option>
            </select>
            <p className={descriptionClass}>Minimum HFO duration</p>
          </div>
        </CardContent>
      </Card>

      {/* Synchronization Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Synchronization Settings</CardTitle>
          <CardDescription>Configure temporal and spatial synchronization parameters</CardDescription>
        </CardHeader>
        <CardContent className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <label htmlFor="temporal-sync" className={labelClass}>
              Temporal Sync (Inter-HFO) *
            </label>
            <select
              id="temporal-sync"
              value={parameters.thresholds.temporal_sync}
              onChange={(e) => updateParameter("thresholds", "temporal_sync", Number(e.target.value))}
              className={selectClass}
            >
              <option value={10}>10 ms</option>
              <option value={12}>12 ms</option>
            </select>
            <p className={descriptionClass}>Inter-HFO interval in any channel &lt; (? ms)</p>
          </div>

          <div className="space-y-2">
            <label htmlFor="spatial-sync" className={labelClass}>
              Spatial Sync (Cross-channels) *
            </label>
            <select
              id="spatial-sync"
              value={parameters.thresholds.spatial_sync}
              onChange={(e) => updateParameter("thresholds", "spatial_sync", Number(e.target.value))}
              className={selectClass}
            >
              <option value={10}>10 ms</option>
              <option value={12}>12 ms</option>
            </select>
            <p className={descriptionClass}>Inter-HFO interval across channels &lt; (? ms)</p>
          </div>
        </CardContent>
      </Card>

      {/* Montage Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Montage Selection</CardTitle>
          <CardDescription>Choose the montage type for signal processing</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-6">
            <label className="flex items-center gap-2">
              <input
                type="radio"
                name="montage"
                value="bipolar"
                checked={parameters.montage.type === "bipolar"}
                onChange={() => updateParameter("montage", "type", "bipolar")}
              />
              Bipolar
            </label>
            <label className="flex items-center gap-2">
              <input
                type="radio"
                name="montage"
                value="average"
                checked={parameters.montage.type === "average"}
                onChange={() => updateParameter("montage", "type", "average")}
              />
              Average
            </label>
            <label className="flex items-center gap-2">
              <input
                type="radio"
                name="montage"
                value="referential"
                checked={parameters.montage.type === "referential"}
                onChange={() => updateParameter("montage", "type", "referential")}
              />
              Referential
            </label>
          </div>

          {parameters.montage.type === "referential" && (
            <div className="space-y-2 mt-4">
              <label htmlFor="reference-channel" className={labelClass}>
                Reference Channel
              </label>
              <select
                id="reference-channel"
                value={parameters.montage.reference || ""}
                onChange={(e) => updateParameter("montage", "reference", e.target.value)}
                className={selectClass}
              >
                <option value="">Select reference channel</option>
                {channels.map((channel) => (
                  <option key={channel} value={channel}>
                    {channel}
                  </option>
                ))}
              </select>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Frequency Filter Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Frequency Filter Settings</CardTitle>
          <CardDescription>Configure the bandpass filter frequency range</CardDescription>
        </CardHeader>
        <CardContent className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <label htmlFor="low-cutoff" className={labelClass}>
              Low Cutoff Filter (Hz) *
            </label>
            <select
              id="low-cutoff"
              value={parameters.frequency.low_cutoff}
              onChange={(e) => updateParameter("frequency", "low_cutoff", Number(e.target.value))}
              className={selectClass}
            >
              <option value={50}>50</option>
              <option value={70}>70</option>
              <option value={80}>80</option>
              <option value={120}>120</option>
            </select>
            <p className={descriptionClass}>Should not exceed 1/3 of sampling rate</p>
          </div>

          <div className="space-y-2">
            <label htmlFor="high-cutoff" className={labelClass}>
              High Cutoff Filter (Hz) *
            </label>
            <select
              id="high-cutoff"
              value={parameters.frequency.high_cutoff}
              onChange={(e) => updateParameter("frequency", "high_cutoff", Number(e.target.value))}
              className={selectClass}
            >
              <option value={300}>300</option>
              <option value={330}>330</option>
              <option value={500}>500</option>
              <option value={600}>600</option>
              <option value={660}>660</option>
            </select>
            <p className={descriptionClass}>Should not exceed 1/3 of sampling rate</p>
          </div>
        </CardContent>
      </Card>

      {/* Channel Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Channel Selection</CardTitle>
          <CardDescription>Select which channels to include in the analysis. All channels are selected by default.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Quick Actions */}
          <div className="flex gap-4">
            <Button type="button" variant="outline" size="sm" onClick={selectAllChannels}>
              Select All
            </Button>
            <Button type="button" variant="outline" size="sm" onClick={deselectAllChannels}>
              Deselect All
            </Button>
            <span className="ml-auto text-sm text-gray-600">
              {selectedChannelCount} of {channels.length} channels selected
            </span>
          </div>

          {/* Channel Groups */}
          <div className="border rounded-lg max-h-96 overflow-y-auto">
            {Object.entries(channelGroups).map(([groupPrefix, groupChannels]) => {
              const isExpanded = expandedGroups[groupPrefix];
              const isFullySelected = isGroupFullySelected(groupPrefix);
              const isPartiallySelected = isGroupPartiallySelected(groupPrefix);

              return (
                <div key={groupPrefix} className="border-b border-gray-200 last:border-b-0">
                  {/* Group Header */}
                  <div className="flex items-center gap-3 p-3 bg-gray-50 hover:bg-gray-100 transition-colors">
                    <button
                      type="button"
                      onClick={() => toggleGroupExpansion(groupPrefix)}
                      className="flex items-center gap-1 text-sm font-medium text-gray-700 hover:text-gray-900"
                    >
                      {isExpanded ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
                      {groupPrefix} ({groupChannels.length})
                    </button>

                    <div className="flex items-center gap-2 ml-auto">
                      <Checkbox
                        checked={isFullySelected}
                        ref={(el) => {
                          if (el) {
                            el.indeterminate = isPartiallySelected && !isFullySelected;
                          }
                        }}
                        onCheckedChange={(checked) => handleGroupSelection(groupPrefix, checked === true)}
                      />
                      <span className="text-xs text-gray-600">
                        {groupChannels.filter((ch) => parameters.channelSelection?.selectedChannels.includes(ch)).length} selected
                      </span>
                    </div>
                  </div>

                  {/* Individual Channels */}
                  {isExpanded && (
                    <div className="p-3">
                      <div className="grid grid-cols-3 gap-2">
                        {groupChannels.map((channel) => (
                          <label key={channel} className="flex items-center gap-2 text-sm cursor-pointer hover:bg-gray-50 p-2 rounded">
                            <Checkbox
                              checked={parameters.channelSelection?.selectedChannels.includes(channel)}
                              onCheckedChange={() => handleChannelSelection(channel)}
                            />
                            <span className="text-gray-700">{channel}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Time Segment Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Select Segment for Analysis</CardTitle>
          <CardDescription>Choose which portion of the recording to analyze</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <label className="flex items-center gap-2">
              <input
                type="radio"
                name="segment"
                value="entire_file"
                checked={parameters.timeSegment?.mode === "entire_file"}
                onChange={() => updateParameter("timeSegment", "mode", "entire_file")}
              />
              <span className="font-medium">Entire File</span>
            </label>

            <div>
              <label className="flex items-center gap-2">
                <input
                  type="radio"
                  name="segment"
                  value="start_end_times"
                  checked={parameters.timeSegment?.mode === "start_end_times"}
                  onChange={() => updateParameter("timeSegment", "mode", "start_end_times")}
                />
                <span className="font-medium">Start / End times</span>
              </label>
              {parameters.timeSegment?.mode === "start_end_times" && (
                <div className="ml-6 mt-3 grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm text-gray-600">Start Time (seconds)</label>
                    <input
                      type="number"
                      className={inputClass}
                      placeholder="0"
                      value={parameters.timeSegment.startTime || ""}
                      onChange={(e) => updateParameter("timeSegment", "startTime", e.target.value ? Number(e.target.value) : undefined)}
                      min={0}
                      step={0.1}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm text-gray-600">End Time (seconds)</label>
                    <input
                      type="number"
                      className={inputClass}
                      placeholder="End of file"
                      value={parameters.timeSegment.endTime || ""}
                      onChange={(e) => updateParameter("timeSegment", "endTime", e.target.value ? Number(e.target.value) : undefined)}
                      min={0}
                      step={0.1}
                    />
                  </div>
                </div>
              )}
            </div>

            <div>
              <label className="flex items-center gap-2">
                <input
                  type="radio"
                  name="segment"
                  value="start_duration"
                  checked={parameters.timeSegment?.mode === "start_duration"}
                  onChange={() => updateParameter("timeSegment", "mode", "start_duration")}
                />
                <span className="font-medium">Start time / Duration</span>
              </label>
              {parameters.timeSegment?.mode === "start_duration" && (
                <div className="ml-6 mt-3 grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm text-gray-600">Start Time (seconds)</label>
                    <input
                      type="number"
                      className={inputClass}
                      placeholder="0"
                      value={parameters.timeSegment.startTime || ""}
                      onChange={(e) => updateParameter("timeSegment", "startTime", e.target.value ? Number(e.target.value) : undefined)}
                      min={0}
                      step={0.1}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm text-gray-600">Duration (seconds)</label>
                    <input
                      type="number"
                      className={inputClass}
                      placeholder="90"
                      value={parameters.timeSegment.duration || ""}
                      onChange={(e) => updateParameter("timeSegment", "duration", e.target.value ? Number(e.target.value) : undefined)}
                      min={1}
                      step={1}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="flex justify-center">
        <Button type="submit" size="lg" disabled={isSubmitting || selectedChannelCount === 0} className="px-12">
          {isSubmitting ? "Processing..." : "Continue"}
        </Button>
      </div>
    </form>
  );
};
