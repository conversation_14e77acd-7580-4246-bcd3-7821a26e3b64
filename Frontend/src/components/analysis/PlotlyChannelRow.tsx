'use client';

import React, { useMemo } from 'react';
import Plot from 'react-plotly.js';
import type { HFOEvent } from '@/types/hfo';
import { HFO_TYPE_COLORS } from '@/types/hfo';
import type { PlotlyTrace } from '@/types/plotly';

interface PlotlyChannelRowProps {
  channelName: string;
  data: number[];
  timeWindow: [number, number];
  samplingRate: number;
  height?: number;
  hfoEvents?: HFOEvent[];
  showHFOMarkers?: boolean;
  isEven?: boolean;
}

export const PlotlyChannelRow: React.FC<PlotlyChannelRowProps> = ({
  channelName,
  data,
  timeWindow,
  samplingRate,
  height = 80,
  hfoEvents = [],
  showHFOMarkers = true,
  isEven = true,
}) => {
  const plotData = useMemo(() => {
    if (!data || data.length === 0) {
      return [];
    }

    const timeAxis = data.map((_, i) => i / samplingRate);

    const startIdx = Math.floor(timeWindow[0] * samplingRate);
    const endIdx = Math.floor(timeWindow[1] * samplingRate);
    const windowedTime = timeAxis.slice(startIdx, endIdx);
    const windowedData = data.slice(startIdx, endIdx);

    const traces: PlotlyTrace[] = [{
      x: windowedTime,
      y: windowedData,
      type: 'scatter',
      mode: 'lines',
      name: channelName,
      line: { color: '#000000', width: 0.8 },
      hovertemplate: `Time: %{x:.2f}s<br>Amplitude: %{y:.2f}μV<extra></extra>`,
    }];

    if (showHFOMarkers && hfoEvents.length > 0 && windowedTime.length > 0) {
      const channelHFOs = hfoEvents.filter(event => event.channel === channelName);

      channelHFOs.forEach(event => {
        const eventType = event.type || 'accepted';
        const color = HFO_TYPE_COLORS[eventType] || HFO_TYPE_COLORS.accepted;

        const rawEndTime = Number.isFinite(event.end_time) ? event.end_time : undefined;
        const eventEndTime = rawEndTime && rawEndTime > event.start_time
          ? rawEndTime
          : event.start_time + 1 / samplingRate;

        const overlapStart = Math.max(event.start_time, timeWindow[0]);
        const overlapEnd = Math.min(eventEndTime, timeWindow[1]);
        if (overlapEnd <= overlapStart) {
          return;
        }

        const eventStartIdx = Math.max(startIdx, Math.floor(overlapStart * samplingRate));
        const eventEndIdx = Math.min(endIdx, Math.ceil(overlapEnd * samplingRate));
        const localStart = Math.max(0, eventStartIdx - startIdx);
        const localEnd = Math.max(localStart + 1, eventEndIdx - startIdx);

        const segmentTime = windowedTime.slice(localStart, localEnd);
        const segmentValues = windowedData.slice(localStart, localEnd);

        if (segmentTime.length === 0) {
          return;
        }

        traces.push({
          x: segmentTime,
          y: segmentValues,
          type: 'scatter',
          mode: 'lines',
          name: `${channelName}-${eventType}`,
          line: { color, width: 1.6 },
          hovertemplate: `HFO (${eventType})<br>Time: %{x:.2f}s<extra></extra>`,
          showlegend: false,
        });
      });
    }

    return traces;
  }, [data, channelName, timeWindow, samplingRate, hfoEvents, showHFOMarkers]);

  const layout = useMemo(() => {
    // Create annotations for HFO counts
    const annotations: Partial<Plotly.Annotations>[] = [{
      x: 0,
      y: 0.5,
      xref: 'paper' as const,
      yref: 'paper' as const,
      text: channelName,
      showarrow: false,
      xanchor: 'right' as const,
      xshift: -10,
      font: {
        size: 11,
        color: '#4a5568',
        family: 'var(--font-sans), system-ui, -apple-system, sans-serif',
      },
    }];

    // Add HFO count annotation
    if (hfoEvents.length > 0) {
      const accepted = hfoEvents.filter(e => (e.type || 'accepted') === 'accepted').length;
      const rejected = hfoEvents.filter(e => e.type && e.type !== 'accepted').length;

      let countText = '';
      if (accepted > 0) countText += `<span style="color:${HFO_TYPE_COLORS.accepted}">${accepted}✓</span> `;
      if (rejected > 0) countText += `<span style="color:${HFO_TYPE_COLORS.rejected}">${rejected}✗</span>`;

      if (countText) {
        annotations.push({
          x: 1,
          y: 0.9,
          xref: 'paper' as const,
          yref: 'paper' as const,
          text: countText,
          showarrow: false,
          xanchor: 'right' as const,
          font: {
            size: 10,
            family: 'var(--font-sans), system-ui, -apple-system, sans-serif',
          },
        });
      }
    }

    return {
      xaxis: {
        range: timeWindow,
        showgrid: false,
        showticklabels: false,
        zeroline: false,
        showline: false,
      },
      yaxis: {
        showgrid: false,
        showticklabels: false,
        zeroline: false,
        showline: false,
        autorange: true,
      },
      height: height,
      margin: { l: 80, r: 60, t: 2, b: 2 },
      hovermode: 'x' as const,
      showlegend: false,
      plot_bgcolor: isEven ? '#fafafa' : '#ffffff',
      paper_bgcolor: isEven ? '#fafafa' : '#ffffff',
      shapes: [],
      annotations: annotations,
    };
  }, [channelName, timeWindow, height, isEven, hfoEvents]);

  const config = {
    displayModeBar: false,
    responsive: true,
    staticPlot: false,
  };

  return (
    <div className="border-b border-gray-100 hover:bg-gray-50 transition-colors">
      <Plot
        data={plotData}
        layout={layout}
        config={config}
        style={{ width: '100%' }}
      />
    </div>
  );
};

export default React.memo(PlotlyChannelRow);
