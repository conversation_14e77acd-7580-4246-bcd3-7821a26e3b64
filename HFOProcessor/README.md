# HFO Processor - Brain Signal Analysis Engine

## What This Does

The HFO Processor is the computational heart of the Biormika platform. It analyzes EEG recordings to automatically detect High-Frequency Oscillations (HFOs) - brief bursts of brain activity that are important biomarkers for epilepsy.

### Medical Context

**What are HFOs?**
- Brief oscillations in brain activity (80-500 Hz frequency)
- Last only 6-100 milliseconds (blink of an eye)
- Occur in epileptic brain tissue
- Help surgeons identify seizure onset zones
- Too brief and subtle for manual detection

**Why Automated Detection?**
- Manual review takes 4-6 hours per recording
- Human reviewers miss 40-60% of HFOs
- Different reviewers find different events
- Automated detection is consistent and complete

## The Detection Algorithm

### How We Find HFOs

```
Raw EEG Signal → Bandpass Filter → Envelope Detection → Threshold → Validation → Results
   (all frequencies)  (80-500 Hz)     (RMS amplitude)    (mean+3σ)   (duration)   (CSV/JSON)
```

1. **Load EEG Data**: Read brain signals from EDF files
2. **Filter Signal**: Keep only high frequencies (80-500 Hz)
3. **Detect Envelope**: Calculate signal strength over time
4. **Find Peaks**: Identify segments above threshold
5. **Validate Events**: Check duration and merge close events
6. **Save Results**: Output timestamps and characteristics

### Visual Example

```
Original EEG:  ～～∼～∼～～⟋⟍⟋⟍⟋～～∼～～
                           ↑ HFO here

After Filter:  --------⟋⟍⟋⟍⟋--------
                       ↑ Isolated HFO

Threshold:     ========|=======
Detection:            [DETECTED]
```

## Quick Start

### Local Testing

```bash
# Build Docker image
docker build -t biormika-hfo .

# Run with sample file
docker run --rm \
  -v $(pwd)/samples:/data \
  -e TEST_MODE=true \
  biormika-hfo python processor.py --file /data/test.edf

# Essential commands
python processor.py --test    # Run with mock data
./build.sh                   # Build and push to ECR
./deploy_hfo_processor.sh    # Deploy to ECS
```

### Production Deployment

```bash
# Build and push container
./build.sh  # Builds for ARM64, pushes to ECR

# Update ECS service
aws ecs update-service \
  --cluster BiormikaHFOCluster \
  --service HFOProcessorService \
  --force-new-deployment \
  --profile biormika
```

## Processing Pipeline

### Input (from SQS Queue)
```json
{
  "job_id": "550e8400",
  "file_key": "uploads/patient_recording.edf",
  "parameters": {
    "frequency_band": [80, 500],
    "threshold": 3.0,
    "min_duration_ms": 6
  },
  "email": "<EMAIL>"
}
```

### Processing Steps
1. **Receive Message**: Poll SQS queue
2. **Download EDF**: Fetch from S3 (up to 1GB)
3. **Process Channels**: Analyze each brain region
4. **Detect HFOs**: Run algorithm on each channel
5. **Save Results**: Upload CSV/JSON to S3
6. **Update Status**: Mark job complete in DynamoDB
7. **Send Email**: Notify user via SES

### Output (CSV Format)
```csv
Channel,Start_Time_sec,End_Time_sec,Duration_ms,Max_Amplitude_uV
Fp1,10.234,10.240,6,125.3
Fp1,15.567,15.575,8,98.7
F3,22.123,22.131,8,156.2
```

## Detection Parameters

| Parameter | Default | Purpose |
|-----------|---------|---------|
| **Frequency Band** | 80-500 Hz | HFO frequency range |
| **Threshold** | 3.0 σ | Sensitivity (lower = more detections) |
| **Min Duration** | 6 ms | Shortest valid HFO |
| **Min Gap** | 10 ms | Merge events closer than this |
| **RMS Window** | 3 ms | Smoothing for envelope |

### Adjusting Sensitivity

```python
# More sensitive (find more HFOs)
{
  "threshold": 2.5,       # Lower threshold
  "min_duration_ms": 5    # Shorter duration
}

# More specific (fewer false positives)
{
  "threshold": 4.0,       # Higher threshold
  "min_duration_ms": 10   # Longer duration
}
```

## Architecture

### Container Configuration
- **Base Image**: Python 3.9 slim
- **Architecture**: ARM64 (Graviton)
- **Dependencies**: NumPy, SciPy, PyEDFlib

### ECS Task Definition
```json
{
  "cpu": "2048",     # 2 vCPU
  "memory": "4096",  # 4 GB RAM
  "platformFamily": "Linux/ARM64"
}
```

### Auto-scaling
- **Min Tasks**: 1 (warm container for fast startup)
- **Max Tasks**: 10
- **Scale Out**: Queue depth > 1
- **Scale In**: Queue empty for 5 min (but maintains 1 warm task)

## Project Structure

```
HFOProcessor/
├── core/
│   ├── hfo_engine/
│   │   ├── edf_reader.py      # EDF file parsing
│   │   ├── hfo_analysis.py    # Detection algorithm
│   │   ├── filters.py         # Signal filtering
│   │   └── result_formatter.py # Output generation
│   ├── validators/            # Input validation
│   └── utils/                # Helpers
├── processor.py              # Main entry point
├── Dockerfile               # Container definition
├── requirements.txt        # Dependencies
└── build.sh               # Build script
```

## Monitoring

```bash
# View processing logs
aws logs tail /ecs/biormika-hfo-processor \
  --profile biormika --follow

# Check task status
aws ecs list-tasks \
  --cluster BiormikaHFOCluster \
  --service HFOProcessorService \
  --profile biormika

# Monitor queue
aws sqs get-queue-attributes \
  --queue-url <URL> \
  --attribute-names ApproximateNumberOfMessages \
  --profile biormika
```

## Performance

### Processing Speed
- **Typical**: 1 hour of EEG in 45 seconds
- **Channels**: 21 channels processed in parallel
- **Memory**: Peak 2GB for 1GB EDF file

### Optimization
- Chunked file reading (10MB blocks)
- NumPy vectorized operations
- Garbage collection after each channel
- Early termination for corrupted files

## Troubleshooting

| Issue | Cause | Solution |
|-------|-------|----------|
| Out of memory | Large file | Increase task memory |
| Slow processing | Many channels | Scale out tasks |
| No detections | High threshold | Lower to 2.5σ |
| Too many detections | Low threshold | Raise to 3.5σ |
| Task not starting | No messages | Check SQS queue |

## Scientific Background

### Filter Design
- **Type**: Butterworth bandpass
- **Order**: 4th order
- **Frequencies**: 80-500 Hz
- **Phase**: Zero-phase (forward-backward)

### Detection Method
- **Envelope**: Root Mean Square (RMS)
- **Baseline**: Mean of filtered signal
- **Threshold**: Baseline + (SD × factor)
- **Validation**: Duration and gap criteria

## Environment Variables

```bash
# AWS Configuration
AWS_REGION=us-east-1
S3_BUCKET=biormika-files
SQS_QUEUE_URL=https://sqs.region.amazonaws.com/xxx
DYNAMODB_TABLE=BiormikaHFOJobTable

# Processing
MAX_CHANNELS=30
CHUNK_SIZE=10485760  # 10MB
LOG_LEVEL=INFO
```

## Dependencies

- **NumPy**: Numerical computing
- **SciPy**: Signal processing filters
- **PyEDFlib**: EDF file reading
- **Boto3**: AWS services
- **Structlog**: Structured logging

## Testing

```bash
# Unit tests
pytest tests/

# Integration test
python processor.py --test-file samples/test.edf

# Performance test
time python processor.py --file large_recording.edf
```

## Future Improvements

- GPU acceleration for filtering
- Real-time streaming processing
- Machine learning detection models
- Cross-channel correlation analysis

## License

Copyright 2024 Biormika. All rights reserved.