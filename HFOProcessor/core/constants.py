"""
Constants for the HFO detection algorithm.

IMPORTANT: These constants are defined here for documentation and future use.
The actual algorithm still uses hardcoded values to preserve integrity.
Any changes should be thoroughly tested before replacing hardcoded values.
"""

# ============================================================================
# ALGORITHM CONSTANTS - Core HFO Detection
# ============================================================================

# Threshold scheme - only scheme 10 is supported
THRESH_SCHEME = 10  # meanHilbert + thresh * stdHilbert

# HFO Detection Parameters (Default values)
DEFAULT_AMPLITUDE_THRESHOLD_1 = 2  # StdDev above energy signal
DEFAULT_AMPLITUDE_THRESHOLD_2 = 2  # StdDev above mean baseline
DEFAULT_MIN_PEAKS_1 = 6  # Min peaks above amplitude threshold 1
DEFAULT_MIN_PEAKS_2 = 3  # Min peaks above amplitude threshold 2
DEFAULT_MIN_HFO_DURATION_MS = 10  # Minimum HFO duration in milliseconds
DEFAULT_TEMPORAL_SYNC_MS = 10  # Inter-HFO interval in channel (ms)
DEFAULT_SPATIAL_SYNC_MS = 10  # Inter-HFO interval across channels (ms)

# Window and filter parameters
WINDOW_LENGTH_MS = 5  # Window length for RMS calculation
MIN_PEAKS_PRESENTATION = 4  # Min peaks for blue line presentation
POWER_THRESHOLD = 1  # Power threshold for HFO detection

# ============================================================================
# SIGNAL PROCESSING CONSTANTS
# ============================================================================

# Sampling rate constraints
MIN_SAMPLING_RATE = 200  # Minimum Hz for HFO detection
MAX_FREQ_FACTOR = 3  # MAX_FREQ = sampling_rate / MAX_FREQ_FACTOR
HFO_BASE_FREQUENCY = 70  # Base HFO frequency for calculations

# Filter parameters
DEFAULT_Q_FACTOR = 35  # Q factor for notch filter
NOTCH_BASE_FREQ = 60  # Base frequency for notch filter (Hz)
DEFAULT_LOW_CUTOFF = 50  # Default low cutoff frequency (Hz)
DEFAULT_HIGH_CUTOFF = 300  # Default high cutoff frequency (Hz)

# Conventional frequency analysis (CFA) range
CFA_LOW_FREQ = 1  # Low frequency for CFA (Hz)
CFA_HIGH_FREQ = 70  # High frequency for CFA (Hz)

# HFO Frequency Classification Parameters
HFO_FREQ = 70  # Threshold frequency for HFO classification (Hz)
POWER_THRESHOLD_RATIO = 1  # Power ratio threshold for HFO detection
MAX_FREQ_THRESHOLD = 0.01  # Minimum fraction of peak power for frequency significance
FFT_LENGTH = 512  # FFT window length for frequency analysis

# ============================================================================
# DATA PROCESSING CONSTANTS
# ============================================================================

# Blank/discontinuity handling
BLANK_DATA_MARKER = 112233  # Marker value for blank data
DISCONTINUITY_LENGTH_MS = 50  # Length to remove at discontinuities (ms)
MIN_BLANK_DURATION_POINTS = 20  # Minimum points for blank detection
BLANK_PROXIMITY_MARGIN = 10  # Margin around blanks (data points)

# Chunk processing
DEFAULT_CHUNK_DURATION = 10  # Default chunk duration for streaming (seconds)
CHUNK_OVERLAP_MS = 100  # Overlap between chunks (milliseconds)

# ============================================================================
# VALIDATION CONSTANTS
# ============================================================================

# Signal quality thresholds
MAX_FLAT_DURATION_MS = 100  # Max duration of flat signal (ms)
MIN_SIGNAL_VARIANCE = 1e-10  # Minimum acceptable signal variance
MAX_DISCONTINUITY_GAP_MS = 1000  # Maximum gap in signal (ms)
MIN_SIGNAL_LENGTH_SEC = 1  # Minimum signal length (seconds)

# File constraints
MIN_FILE_SIZE_BYTES = 256  # Minimum EDF file size
MAX_CHANNELS = 256  # Maximum number of channels supported

# ============================================================================
# MONTAGE CONSTANTS
# ============================================================================

# Montage types
MONTAGE_BIPOLAR = "Bipolar montage"
MONTAGE_AVERAGE = "Average montage"
MONTAGE_REFERENTIAL = "Referential montage"

# Montage patterns
BIPOLAR_PATTERN = r'(\w+)(\d+)-(\w+)(\d+)'  # Pattern for bipolar labels
CHANNEL_NUMBER_PATTERN = r'(\d+)'  # Pattern for channel numbers

# ============================================================================
# OUTPUT AND DISPLAY CONSTANTS
# ============================================================================

# HFO characteristics display precision
FREQUENCY_PRECISION = 2  # Decimal places for frequency display
AMPLITUDE_PRECISION = 3  # Decimal places for amplitude display
TIME_PRECISION = 4  # Decimal places for time display

# Result limits
MAX_HFOS_PER_CHANNEL = 10000  # Maximum HFOs per channel
MAX_TOTAL_HFOS = 100000  # Maximum total HFOs

# ============================================================================
# ERROR MESSAGES
# ============================================================================

ERROR_UNKNOWN_MONTAGE = "ERROR: Unknown montage"
ERROR_INVALID_THRESHOLD_SCHEME = "ERROR: only thresh_scheme 10 is available....exiting"
ERROR_NO_DATA = "ERROR: No data available for analysis"
ERROR_SAMPLING_RATE_TOO_LOW = "ERROR: Sampling rate too low for HFO detection"

# ============================================================================
# NOISE REMOVAL CONSTANTS (Currently not used but defined for future)
# ============================================================================

NOISE_FREQ_REMOVAL_ENABLED = 0  # 1 to enable, 0 to disable
NOISE_FREQ_TOLERANCE_HZ = 2  # +/- Hz from noise frequency
DEFAULT_NOISE_FREQUENCIES = []  # List of noise frequencies to remove

# ============================================================================
# PROCESSING FLAGS (Default values)
# ============================================================================

FILTER_ON = 1  # 1 = filtering on, 0 = off
NOTCH_FILTER_ON = 1  # 1 = notch filtering on, 0 = off
FILTER_CFA_ON = 1  # 1 = conventional filtering on, 0 = off
REMOVE_DISCONTINUITIES = 1  # 1 = remove discontinuous sections, 0 = keep
REJECT_BLANKS = 0  # 1 = remove HFOs near blanks, 0 = keep