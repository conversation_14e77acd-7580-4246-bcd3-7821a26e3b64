"""
HFO Analyzer Module
Main orchestrator for HFO analysis using extracted modules
"""

import numpy as np
import logging
import os
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime

from .montage_processor import MontageProcessor
from .signal_filtering import SignalFilter
from .hfo_detector import HFODetector
from .blank_processor import BlankProcessor
from .hfo_characteristics import calculate_HFO_characteristics
from .hfo_proximity import is_hfo_nearby

from core.constants import (
    DEFAULT_LOW_CUTOFF,
    DEFAULT_HIGH_CUTOFF,
    DEFAULT_AMPLITUDE_THRESHOLD_1,
    DEFAULT_AMPLITUDE_THRESHOLD_2,
    DEFAULT_MIN_PEAKS_1,
    DEFAULT_MIN_PEAKS_2,
    DEFAULT_MIN_HFO_DURATION_MS,
    DEFAULT_TEMPORAL_SYNC_MS,
    POWER_THRESHOLD
)

logger = logging.getLogger(__name__)


class HFOAnalyzer:
    """Main orchestrator for HFO analysis"""

    def __init__(self, sampling_rate: float):
        """
        Initialize HFO analyzer with all required processors

        Args:
            sampling_rate: Sampling frequency in Hz
        """
        self.sampling_rate = sampling_rate

        # Initialize processors
        self.montage_processor = MontageProcessor()
        self.signal_filter = SignalFilter(sampling_rate)
        self.hfo_detector = HFODetector(sampling_rate)
        self.blank_processor = BlankProcessor(sampling_rate)

    def analyze(self,
                eeg_data: Dict[str, Any],
                input_file_path: str,
                analysis_start: float,
                analysis_end: float,
                montage: str,
                reference_channel: Optional[str] = None,
                low_cutoff: float = DEFAULT_LOW_CUTOFF,
                high_cutoff: float = DEFAULT_HIGH_CUTOFF,
                parameters: Optional[Dict[str, Any]] = None,
                gui_output: Optional[callable] = None) -> Dict[str, Any]:
        """
        Main HFO analysis pipeline

        Args:
            eeg_data: Dictionary containing EEG data and metadata
            input_file_path: Path to the input file
            analysis_start: Start time for analysis (seconds)
            analysis_end: End time for analysis (seconds)
            montage: Montage type to apply
            reference_channel: Reference channel for referential montage
            low_cutoff: Low frequency cutoff for HFO detection
            high_cutoff: High frequency cutoff for HFO detection
            parameters: Analysis parameters dictionary
            gui_output: Optional callback for status messages

        Returns:
            Dictionary containing analysis results
        """
        try:
            # Initialize results
            results = {
                'success': False,
                'file_path': input_file_path,
                'analysis_start': analysis_start,
                'analysis_end': analysis_end,
                'montage': montage,
                'parameters': parameters or {}
            }

            # Extract parameters
            params = self._extract_parameters(parameters)

            # Extract time window
            signal, channel_labels = self._extract_time_window(
                eeg_data, analysis_start, analysis_end, gui_output
            )

            # Process blanks and discontinuities
            blank_results = self.blank_processor.process_blanks_for_analysis(
                signal,
                channel_labels,
                visual_discontinuities=params.get('visual_discontinuities'),
                remove_discontinuities=params['remove_discontinuities'],
                gui_output=gui_output
            )

            signal = blank_results['processed_signal']
            blank_starts = blank_results['blank_starts']
            blank_ends = blank_results['blank_ends']

            # Apply montage
            if gui_output:
                gui_output(f"\nApplying {montage}...")

            signal, montage_labels = self.montage_processor.apply_montage(
                signal, channel_labels, montage, reference_channel, gui_output
            )

            # Apply filtering
            filtered_signals = self.signal_filter.apply_all_filters(
                signal,
                low_cutoff=low_cutoff,
                high_cutoff=high_cutoff,
                apply_notch=params['apply_notch'],
                apply_bandpass=params['apply_bandpass'],
                apply_cfa=params['apply_cfa'],
                gui_output=gui_output
            )

            hfo_signal = filtered_signals['hfo_filtered']
            cfa_signal = filtered_signals.get('cfa_filtered')

            # Detect HFOs
            detection_results = self.hfo_detector.detect_hfos(
                hfo_signal, params, gui_output
            )

            # Filter HFOs near blanks if requested
            if params['reject_blanks'] and len(blank_starts) > 0:
                start_indices, end_indices = self.hfo_detector.check_hfos_near_blanks(
                    detection_results['start_indices'],
                    detection_results['end_indices'],
                    blank_starts.tolist(),
                    blank_ends.tolist(),
                    margin=params.get('blank_margin', 10)
                )
                detection_results['start_indices'] = start_indices
                detection_results['end_indices'] = end_indices

            # Calculate HFO characteristics
            hfo_events = self._create_hfo_events(
                detection_results,
                hfo_signal,
                montage_labels,
                params
            )

            # Calculate summary statistics
            num_true_hfos = len(hfo_events['true_hfos'])
            num_rejected_peaks = len(hfo_events['rejected_peaks'])
            num_lfo = len(hfo_events['low_frequency'])
            num_noise = len(hfo_events['noise_frequency'])
            total_candidates = num_true_hfos + num_rejected_peaks + num_lfo + num_noise

            # Compile results
            results.update({
                'success': True,
                'num_channels': len(montage_labels),
                'num_hfos': num_true_hfos,
                'hfo_events': hfo_events,
                'hfo_statistics': {
                    'true_hfos': num_true_hfos,
                    'rejected_peaks': num_rejected_peaks,
                    'low_frequency': num_lfo,
                    'noise_frequency': num_noise,
                    'total_candidates': total_candidates,
                    'acceptance_rate': num_true_hfos / total_candidates if total_candidates > 0 else 0
                },
                'channel_labels': montage_labels,
                'blank_statistics': blank_results['statistics'],
                'detection_results': detection_results,
                'analysis_duration': analysis_end - analysis_start,
                'sampling_rate': self.sampling_rate
            })

            if gui_output:
                gui_output(f"\nAnalysis complete: {num_true_hfos} true HFOs detected")
                gui_output(f"  - Rejected (peaks): {num_rejected_peaks}")
                gui_output(f"  - Rejected (low freq): {num_lfo}")
                gui_output(f"  - Rejected (noise): {num_noise}")

            return results

        except Exception as e:
            logger.error(f"HFO analysis failed: {e}")
            if gui_output:
                gui_output(f"ERROR: Analysis failed - {str(e)}")
            return {'success': False, 'error': str(e)}

    def _extract_parameters(self, parameters: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Extract and validate analysis parameters

        Args:
            parameters: Input parameters dictionary

        Returns:
            Processed parameters with defaults
        """
        params = parameters or {}

        return {
            # Threshold parameters
            'amplitude_1': params.get('amplitude_1', DEFAULT_AMPLITUDE_THRESHOLD_1),
            'amplitude_2': params.get('amplitude_2', DEFAULT_AMPLITUDE_THRESHOLD_2),
            'peaks_1': params.get('peaks_1', DEFAULT_MIN_PEAKS_1),
            'peaks_2': params.get('peaks_2', DEFAULT_MIN_PEAKS_2),
            'duration': params.get('duration', DEFAULT_MIN_HFO_DURATION_MS),
            'temporal_sync': params.get('temporal_sync', DEFAULT_TEMPORAL_SYNC_MS),
            'power_threshold': params.get('power_threshold', POWER_THRESHOLD),
            'window_length': params.get('window_length', 5),

            # Processing flags
            'apply_notch': params.get('apply_notch', True),
            'apply_bandpass': params.get('apply_bandpass', True),
            'apply_cfa': params.get('apply_cfa', True),
            'remove_discontinuities': params.get('remove_discontinuities', True),
            'reject_blanks': params.get('reject_blanks', False),

            # Additional parameters
            'visual_discontinuities': params.get('visual_discontinuities', []),
            'blank_margin': params.get('blank_margin', 10),
            'threshold_scheme': params.get('threshold_scheme', 10),

            # Frequency analysis parameters
            'noise_freq_removal': params.get('noise_freq_removal', 0),
            'noise_freq_vector': params.get('noise_freq_vector', [])
        }

    def _extract_time_window(self,
                           eeg_data: Dict[str, Any],
                           start_time: float,
                           end_time: float,
                           gui_output: Optional[callable] = None) -> Tuple[np.ndarray, List[str]]:
        """
        Extract the specified time window from EEG data

        Args:
            eeg_data: EEG data dictionary
            start_time: Start time in seconds
            end_time: End time in seconds
            gui_output: Optional callback

        Returns:
            Tuple of (signal_array, channel_labels)
        """
        # Get sampling rate
        srate = eeg_data.get('srate', self.sampling_rate)

        # Calculate sample indices
        start_sample = int(start_time * srate)
        end_sample = int(end_time * srate)

        # Extract data
        data = eeg_data.get('data')
        if isinstance(data, list):
            data = np.array(data)

        # Get time window
        if end_sample > data.shape[1]:
            end_sample = data.shape[1]

        windowed_data = data[:, start_sample:end_sample]

        # Get channel labels
        channel_labels = eeg_data.get('chanlocs', [])
        if not channel_labels:
            channel_labels = [f"CH{i+1}" for i in range(data.shape[0])]

        if gui_output:
            gui_output(f"Extracted {end_time - start_time:.2f}s window: "
                      f"{windowed_data.shape[1]} samples from {len(channel_labels)} channels")

        return windowed_data, channel_labels

    def _create_hfo_events(self,
                          detection_results: Dict[str, Any],
                          signal: np.ndarray,
                          channel_labels: List[str],
                          parameters: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Create HFO event records with characteristics for all categories

        Args:
            detection_results: Detection results from HFO detector
            signal: Filtered signal
            channel_labels: Channel labels
            parameters: Analysis parameters

        Returns:
            Dictionary with HFO events categorized by type
        """
        # Initialize event lists for each category
        true_hfo_events = []
        rejected_peak_events = []
        lfo_events = []
        noise_events = []

        # Process true HFOs
        start_indices = detection_results['start_indices']
        end_indices = detection_results['end_indices']

        for ch_idx in range(len(channel_labels)):
            channel_name = channel_labels[ch_idx]

            # Process true HFOs
            for i in range(start_indices.shape[1]):
                if start_indices[ch_idx, i] == 0:
                    break

                event = self._create_single_event(
                    signal, ch_idx, channel_name,
                    int(start_indices[ch_idx, i]),
                    int(end_indices[ch_idx, i]),
                    'true_hfo'
                )
                true_hfo_events.append(event)

            # Process rejected by peak criteria
            rejected_starts = detection_results.get('rejected_start', np.zeros((len(channel_labels), 1)))
            rejected_ends = detection_results.get('rejected_end', np.zeros((len(channel_labels), 1)))
            for i in range(rejected_starts.shape[1]):
                if rejected_starts[ch_idx, i] == 0:
                    break

                event = self._create_single_event(
                    signal, ch_idx, channel_name,
                    int(rejected_starts[ch_idx, i]),
                    int(rejected_ends[ch_idx, i]),
                    'rejected_peaks'
                )
                rejected_peak_events.append(event)

            # Process low frequency oscillations
            lfo_starts = detection_results.get('lfo_start', np.zeros((len(channel_labels), 1)))
            lfo_ends = detection_results.get('lfo_end', np.zeros((len(channel_labels), 1)))
            for i in range(lfo_starts.shape[1]):
                if lfo_starts[ch_idx, i] == 0:
                    break

                event = self._create_single_event(
                    signal, ch_idx, channel_name,
                    int(lfo_starts[ch_idx, i]),
                    int(lfo_ends[ch_idx, i]),
                    'low_frequency'
                )
                lfo_events.append(event)

            # Process noise frequency oscillations
            noise_starts = detection_results.get('noise_start', np.zeros((len(channel_labels), 1)))
            noise_ends = detection_results.get('noise_end', np.zeros((len(channel_labels), 1)))
            for i in range(noise_starts.shape[1]):
                if noise_starts[ch_idx, i] == 0:
                    break

                event = self._create_single_event(
                    signal, ch_idx, channel_name,
                    int(noise_starts[ch_idx, i]),
                    int(noise_ends[ch_idx, i]),
                    'noise_frequency'
                )
                noise_events.append(event)

        return {
            'true_hfos': true_hfo_events,
            'rejected_peaks': rejected_peak_events,
            'low_frequency': lfo_events,
            'noise_frequency': noise_events
        }

    def _create_single_event(self, signal: np.ndarray, ch_idx: int,
                            channel_name: str, start_idx: int, end_idx: int,
                            event_type: str) -> Dict[str, Any]:
        """
        Create a single HFO event record

        Args:
            signal: Filtered signal
            ch_idx: Channel index
            channel_name: Channel name
            start_idx: Start sample index
            end_idx: End sample index
            event_type: Type of event

        Returns:
            HFO event dictionary
        """
        duration_ms = (end_idx - start_idx) / self.sampling_rate * 1000
        start_time_s = start_idx / self.sampling_rate
        end_time_s = end_idx / self.sampling_rate

        # Extract HFO segment
        hfo_segment = signal[ch_idx, start_idx:end_idx]

        return {
            'channel': channel_name,
            'channel_index': ch_idx,
            'start_sample': start_idx,
            'end_sample': end_idx,
            'start_time': start_time_s,
            'end_time': end_time_s,
            'duration_ms': duration_ms,
            'amplitude': float(np.max(np.abs(hfo_segment))),
            'mean_amplitude': float(np.mean(np.abs(hfo_segment))),
            'rms': float(np.sqrt(np.mean(hfo_segment ** 2))),
            'event_type': event_type
        }

    def _count_total_hfos(self, start_indices: np.ndarray) -> int:
        """
        Count total number of HFOs across all channels

        Args:
            start_indices: Start indices array

        Returns:
            Total HFO count
        """
        return np.sum(start_indices > 0)